{"LOG": true, "CLAUDE_PATH": "", "HOST": "127.0.0.1", "PORT": 3456, "APIKEY": "", "transformers": [], "Providers": [{"name": "gemini-blance", "api_base_url": "https://gemini.xing2006.me/v1/chat/completions", "api_key": "sk-123456", "models": ["gemini-2.5-pro"], "transformer": {"use": []}}, {"name": "mota", "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions", "api_key": "ms-6ad68485-1cc5-4553-bc85-c21321e26a11", "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct"], "transformer": {"use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"]}}], "Router": {"default": "gemini-blance,gemini-2.5-pro", "background": "gemini-blance,gemini-2.5-pro", "think": "gemini-blance,gemini-2.5-pro", "longContext": "gemini-blance,gemini-2.5-pro", "webSearch": "gemini-blance,gemini-2.5-pro"}}